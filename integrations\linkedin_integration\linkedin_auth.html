<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn API Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #0077b5, #00a0dc);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        h1 {
            color: #0077b5;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #0077b5;
            background-color: #f9f9f9;
            border-radius: 0 10px 10px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            border-color: #0077b5;
            outline: none;
        }
        button {
            background-color: #0077b5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #005885;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #0077b5;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💼 LinkedIn API Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> LinkedIn API has strict rate limits and requires approval for messaging features.
            This integration uses Unipile API for professional messaging including InMail and connection messaging.
        </div>

        <div class="step">
            <h3>🚀 Method 1: Unipile API (Recommended for Professional Messaging)</h3>
            <div class="info">
                <strong>Unipile provides unified access to LinkedIn messaging including InMail, connection messaging, and company page messaging.</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       value="RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect via Unipile</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>
        </div>

        <div class="step">
            <h3>📱 Method 2: LinkedIn API (For Content & Connections)</h3>
            <div class="info">
                <strong>LinkedIn API for content management and connection operations.</strong>
            </div>
        </div>

        <div class="step">
            <h3>Step 1: Create LinkedIn App</h3>
            <p>1. Go to <a href="https://www.linkedin.com/developers/" target="_blank">LinkedIn Developers</a></p>
            <p>2. Sign in with your LinkedIn account</p>
            <p>3. Click "Create App" and fill in the required information</p>
            <p>4. Add your company page (required for app creation)</p>
            <p>5. Verify your app and company page</p>
        </div>

        <div class="step">
            <h3>Step 2: Configure App Permissions</h3>
            <p>Request the following permissions for your app:</p>
            <ul>
                <li><strong>r_liteprofile:</strong> Basic profile information</li>
                <li><strong>r_emailaddress:</strong> Email address access</li>
                <li><strong>w_member_social:</strong> Post on behalf of user</li>
                <li><strong>r_member_social:</strong> Read social stream</li>
                <li><strong>rw_company_admin:</strong> Company page management (if applicable)</li>
            </ul>
            <p><strong>Note:</strong> Some permissions require LinkedIn approval and may take time to process.</p>
        </div>

        <div class="step">
            <h3>Step 3: Get Your App Credentials</h3>
            
            <form id="linkedinConfigForm">
                <div class="form-group">
                    <label for="clientId">Client ID:</label>
                    <input type="text" id="clientId" name="clientId" 
                           placeholder="Your LinkedIn app client ID">
                </div>
                
                <div class="form-group">
                    <label for="clientSecret">Client Secret:</label>
                    <input type="password" id="clientSecret" name="clientSecret" 
                           placeholder="Your LinkedIn app client secret">
                </div>
                
                <div class="form-group">
                    <label for="accessToken">Access Token:</label>
                    <input type="password" id="accessToken" name="accessToken" 
                           placeholder="Your access token (obtained through OAuth)">
                </div>
                
                <div class="form-group">
                    <label for="refreshToken">Refresh Token:</label>
                    <input type="password" id="refreshToken" name="refreshToken" 
                           placeholder="Your refresh token">
                </div>
                
                <div class="form-group">
                    <label for="personId">Person ID:</label>
                    <input type="text" id="personId" name="personId" 
                           placeholder="Your LinkedIn person ID">
                </div>
                
                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>
            
            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: OAuth 2.0 Authorization</h3>
            <p>To get access tokens, implement OAuth 2.0 flow:</p>
            <div class="code">
                Authorization URL: https://www.linkedin.com/oauth/v2/authorization<br>
                Token URL: https://www.linkedin.com/oauth/v2/accessToken
            </div>
            <p>Required parameters:</p>
            <ul>
                <li><strong>response_type:</strong> code</li>
                <li><strong>client_id:</strong> Your app's client ID</li>
                <li><strong>redirect_uri:</strong> Your registered redirect URI</li>
                <li><strong>scope:</strong> Requested permissions (space-separated)</li>
            </ul>
            <button type="button" onclick="initiateOAuth()">Start OAuth Flow</button>
            <div id="oauthResult"></div>
        </div>

        <div class="step">
            <h3>Step 5: Profile Information</h3>
            <p>Once configured, get your LinkedIn profile information:</p>
            <button type="button" onclick="getProfile()">Get Profile Info</button>
            <div id="profileResult"></div>
        </div>

        <div class="step">
            <h3>Step 6: Professional Messaging Features</h3>
            <div class="info">
                <strong>Enhanced messaging capabilities via Unipile API including InMail and connection messaging.</strong>
            </div>

            <h4>InMail Messaging</h4>
            <div class="form-group">
                <label for="inmailRecipientId">Recipient Person ID:</label>
                <input type="text" id="inmailRecipientId" placeholder="LinkedIn person ID for InMail">
            </div>
            <div class="form-group">
                <label for="inmailSubject">InMail Subject:</label>
                <input type="text" id="inmailSubject" placeholder="Professional subject line (max 200 chars)">
            </div>
            <div class="form-group">
                <label for="inmailBody">InMail Message:</label>
                <textarea id="inmailBody" rows="4" placeholder="Professional InMail message (max 1900 chars)"></textarea>
            </div>
            <button type="button" onclick="sendInMail()">Send InMail</button>
            <div id="inmailResult"></div>

            <h4>Connection Messaging</h4>
            <div class="form-group">
                <label for="connectionMsgRecipientId">Connection Person ID:</label>
                <input type="text" id="connectionMsgRecipientId" placeholder="LinkedIn person ID of connection">
            </div>
            <div class="form-group">
                <label for="connectionMsgBody">Connection Message:</label>
                <textarea id="connectionMsgBody" rows="3" placeholder="Message to your LinkedIn connection"></textarea>
            </div>
            <button type="button" onclick="sendConnectionMessage()">Send Connection Message</button>
            <div id="connectionMsgResult"></div>

            <h4>Traditional LinkedIn API Messaging</h4>
            <div class="form-group">
                <label for="recipientId">Recipient Person ID:</label>
                <input type="text" id="recipientId" placeholder="LinkedIn person ID of recipient">
            </div>
            <div class="form-group">
                <label for="messageSubject">Message Subject:</label>
                <input type="text" id="messageSubject" placeholder="Subject of your message">
            </div>
            <div class="form-group">
                <label for="messageBody">Message Body:</label>
                <textarea id="messageBody" rows="4" placeholder="Your message content"></textarea>
            </div>
            <button type="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="messageResult"></div>
        </div>

        <div class="step">
            <h3>Step 7: Company Page Messaging</h3>
            <div class="info">
                <strong>Send messages from your company page via Unipile API.</strong>
            </div>

            <div class="form-group">
                <label for="companyId">Company ID:</label>
                <input type="text" id="companyId" placeholder="Your LinkedIn company ID">
            </div>
            <div class="form-group">
                <label for="companyMsgRecipientId">Recipient Person ID:</label>
                <input type="text" id="companyMsgRecipientId" placeholder="LinkedIn person ID of recipient">
            </div>
            <div class="form-group">
                <label for="companyMessage">Company Message:</label>
                <textarea id="companyMessage" rows="4" placeholder="Message from your company page"></textarea>
            </div>
            <button type="button" onclick="sendCompanyPageMessage()">Send Company Message</button>
            <div id="companyMsgResult"></div>
        </div>

        <div class="step">
            <h3>Step 8: Bulk InMail Campaigns</h3>
            <div class="info">
                <strong>Send personalized InMails to multiple recipients via Unipile API.</strong>
            </div>

            <div class="form-group">
                <label for="bulkInmailRecipients">Recipients (JSON format):</label>
                <textarea id="bulkInmailRecipients" rows="6" placeholder='[{"id": "person_id_1", "name": "John Doe", "company": "ABC Corp"}, {"id": "person_id_2", "name": "Jane Smith", "company": "XYZ Inc"}]'></textarea>
            </div>
            <div class="form-group">
                <label for="bulkInmailSubject">Subject Template:</label>
                <input type="text" id="bulkInmailSubject" placeholder="Hi {name}, opportunity at {company}">
            </div>
            <div class="form-group">
                <label for="bulkInmailTemplate">Message Template:</label>
                <textarea id="bulkInmailTemplate" rows="4" placeholder="Hi {name}, I noticed your work at {company}. I'd love to discuss..."></textarea>
            </div>
            <div class="form-group">
                <label for="inmailDelay">Delay between InMails (seconds):</label>
                <input type="number" id="inmailDelay" value="5" min="3" max="60">
            </div>
            <button type="button" onclick="sendBulkInMails()">Send Bulk InMails</button>
            <div id="bulkInmailResult"></div>
        </div>

        <div class="step">
            <h3>Step 9: Content Publishing</h3>
            <p>Create and publish posts to LinkedIn:</p>
            <div class="form-group">
                <label for="postContent">Post Content:</label>
                <textarea id="postContent" rows="4" placeholder="Your LinkedIn post content (max 3000 characters)"></textarea>
            </div>
            <div class="form-group">
                <label for="postVisibility">Visibility:</label>
                <select id="postVisibility">
                    <option value="PUBLIC">Public</option>
                    <option value="CONNECTIONS">Connections Only</option>
                </select>
            </div>
            <button type="button" onclick="createPost()">Create Post</button>
            <div id="postResult"></div>
        </div>

        <div class="step">
            <h3>Step 10: Enhanced Connection Management</h3>
            <p>Manage your LinkedIn connections:</p>
            <button type="button" onclick="getConnections()">Get Connections</button>
            
            <div class="form-group" style="margin-top: 15px;">
                <label for="connectionPersonId">Person ID for Connection Request:</label>
                <input type="text" id="connectionPersonId" placeholder="Person ID to send connection request">
            </div>
            <div class="form-group">
                <label for="connectionMessage">Connection Message:</label>
                <textarea id="connectionMessage" rows="2" placeholder="Personal message for connection request"></textarea>
            </div>
            <button type="button" onclick="sendConnectionRequest()">Send Connection Request</button>
            <div id="connectionResult"></div>
        </div>

        <div class="step">
            <h3>Step 11: Search & Discovery</h3>
            <p>Search for people and companies on LinkedIn:</p>
            <div class="form-group">
                <label for="searchKeywords">Keywords:</label>
                <input type="text" id="searchKeywords" placeholder="Search keywords">
            </div>
            <div class="form-group">
                <label for="searchCompany">Company:</label>
                <input type="text" id="searchCompany" placeholder="Company name">
            </div>
            <div class="form-group">
                <label for="searchIndustry">Industry:</label>
                <input type="text" id="searchIndustry" placeholder="Industry">
            </div>
            <button type="button" onclick="searchPeople()">Search People</button>
            <div id="searchResult"></div>
        </div>

        <div class="step">
            <h3>Step 12: Rate Limits & Best Practices</h3>
            <ul>
                <li><strong>Rate Limits:</strong> 100 requests per minute, 500 per hour</li>
                <li><strong>Messaging Limits:</strong> Very restricted, only 1st-degree connections</li>
                <li><strong>Content Policy:</strong> Follow LinkedIn's professional standards</li>
                <li><strong>Spam Prevention:</strong> Don't send unsolicited messages</li>
                <li><strong>API Approval:</strong> Some features require LinkedIn approval</li>
                <li><strong>Data Usage:</strong> Respect user privacy and data protection laws</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkUnipileStatus, 30000);

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            fetch('/api/linkedin/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus(); // Refresh status
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            fetch('/api/linkedin/unipile/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const accounts = data.accounts || [];

                    if (accounts.length > 0) {
                        let html = '<div class="success"><strong>✅ Connected LinkedIn Accounts:</strong><br>';
                        accounts.forEach(account => {
                            html += `💼 Account ID: ${account.id}<br>`;
                            html += `👤 Name: ${account.name || 'N/A'}<br>`;
                            html += `📧 Email: ${account.email || 'N/A'}<br>`;
                            html += `🏢 Company: ${account.company || 'N/A'}<br>`;
                            html += `🔗 Status: ${account.status || 'Connected'}<br><br>`;
                        });
                        html += '</div>';
                        statusDiv.innerHTML = html;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="info">
                                <strong>📋 Unipile API Connected</strong><br>
                                No LinkedIn accounts found. To add a LinkedIn account:<br>
                                1. Visit your <a href="https://dashboard.unipile.com" target="_blank">Unipile Dashboard</a><br>
                                2. Click "Add Account" → "LinkedIn"<br>
                                3. Follow the authentication process<br>
                                4. Return here and click "Check Connection Status"<br><br>
                                <strong>💡 Note:</strong> LinkedIn accounts connected via Unipile will appear in your dashboard and enable professional messaging features like InMail.
                            </div>
                        `;
                    }
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }

        function saveConfig() {
            const config = {
                unipile_api_key: document.getElementById('unipileApiKey').value,
                client_id: document.getElementById('clientId').value,
                client_secret: document.getElementById('clientSecret').value,
                access_token: document.getElementById('accessToken').value,
                refresh_token: document.getElementById('refreshToken').value,
                person_id: document.getElementById('personId').value
            };
            
            fetch('/api/linkedin/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/linkedin/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function initiateOAuth() {
            const clientId = document.getElementById('clientId').value;
            if (!clientId) {
                document.getElementById('oauthResult').innerHTML = 
                    '<div class="error">❌ Please enter Client ID first</div>';
                return;
            }
            
            const scope = 'r_liteprofile r_emailaddress w_member_social';
            const redirectUri = 'YOUR_REDIRECT_URI'; // Replace with your actual redirect URI
            const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}`;
            
            document.getElementById('oauthResult').innerHTML = 
                `<div class="info">Open this URL to authorize: <a href="${authUrl}" target="_blank">Authorize App</a></div>`;
        }
        
        function getProfile() {
            fetch('/api/linkedin/profile')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('profileResult');
                if (data.success) {
                    const profile = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>Profile Information:</strong><br>
                            Name: ${profile.firstName} ${profile.lastName}<br>
                            Headline: ${profile.headline}<br>
                            Industry: ${profile.industry}<br>
                            ID: ${profile.id}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get profile: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('profileResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendInMail() {
            const recipientId = document.getElementById('inmailRecipientId').value;
            const subject = document.getElementById('inmailSubject').value;
            const body = document.getElementById('inmailBody').value;

            if (!recipientId || !subject || !body) {
                document.getElementById('inmailResult').innerHTML =
                    '<div class="error">❌ Please fill in all InMail fields</div>';
                return;
            }

            fetch('/api/linkedin/send-inmail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    subject: subject,
                    message_body: body
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('inmailResult');
                if (data.success) {
                    const method = data.method || 'unknown';
                    resultDiv.innerHTML = `<div class="success">✅ InMail sent successfully via ${method}!</div>`;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send InMail: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('inmailResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendConnectionMessage() {
            const recipientId = document.getElementById('connectionMsgRecipientId').value;
            const message = document.getElementById('connectionMsgBody').value;

            if (!recipientId || !message) {
                document.getElementById('connectionMsgResult').innerHTML =
                    '<div class="error">❌ Please fill in all connection message fields</div>';
                return;
            }

            fetch('/api/linkedin/send-connection-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('connectionMsgResult');
                if (data.success) {
                    const method = data.method || 'unknown';
                    resultDiv.innerHTML = `<div class="success">✅ Connection message sent successfully via ${method}!</div>`;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send connection message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionMsgResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendCompanyPageMessage() {
            const companyId = document.getElementById('companyId').value;
            const recipientId = document.getElementById('companyMsgRecipientId').value;
            const message = document.getElementById('companyMessage').value;

            if (!companyId || !recipientId || !message) {
                document.getElementById('companyMsgResult').innerHTML =
                    '<div class="error">❌ Please fill in all company message fields</div>';
                return;
            }

            fetch('/api/linkedin/send-company-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: companyId,
                    recipient_id: recipientId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('companyMsgResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Company page message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send company message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('companyMsgResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendBulkInMails() {
            const recipientsText = document.getElementById('bulkInmailRecipients').value;
            const subjectTemplate = document.getElementById('bulkInmailSubject').value;
            const messageTemplate = document.getElementById('bulkInmailTemplate').value;
            const delay = parseFloat(document.getElementById('inmailDelay').value) || 5;

            if (!recipientsText || !subjectTemplate || !messageTemplate) {
                document.getElementById('bulkInmailResult').innerHTML =
                    '<div class="error">❌ Please fill in all bulk InMail fields</div>';
                return;
            }

            let recipients;
            try {
                recipients = JSON.parse(recipientsText);
            } catch (e) {
                document.getElementById('bulkInmailResult').innerHTML =
                    '<div class="error">❌ Invalid JSON format for recipients</div>';
                return;
            }

            // Show sending status
            document.getElementById('bulkInmailResult').innerHTML =
                `<div class="info">📤 Sending bulk InMails to ${recipients.length} recipients...</div>`;

            fetch('/api/linkedin/send-bulk-inmails', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    subject_template: subjectTemplate,
                    message_template: messageTemplate,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkInmailResult');
                if (data.success) {
                    const results = data.results || [];
                    const successful = results.filter(r => !r.result.error).length;
                    const failed = results.length - successful;

                    let html = `<div class="success">✅ Bulk InMail campaign completed!<br>`;
                    html += `Successful: ${successful}, Failed: ${failed}<br><br>`;
                    html += '<strong>Details:</strong><br>';

                    results.forEach((result, index) => {
                        const status = result.result.error ? '❌' : '✅';
                        const method = result.method || 'unknown';
                        html += `${status} ${result.recipient_id} (${method})<br>`;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Bulk InMail campaign failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkInmailResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendTestMessage() {
            const recipientId = document.getElementById('recipientId').value;
            const subject = document.getElementById('messageSubject').value;
            const body = document.getElementById('messageBody').value;
            
            if (!recipientId || !subject || !body) {
                document.getElementById('messageResult').innerHTML = 
                    '<div class="error">❌ Please fill in all message fields</div>';
                return;
            }
            
            fetch('/api/linkedin/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    subject: subject,
                    message_body: body
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('messageResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('messageResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function createPost() {
            const content = document.getElementById('postContent').value;
            const visibility = document.getElementById('postVisibility').value;
            
            if (!content) {
                document.getElementById('postResult').innerHTML = 
                    '<div class="error">❌ Please enter post content</div>';
                return;
            }
            
            fetch('/api/linkedin/create-post', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    visibility: visibility
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('postResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Post created successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to create post: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('postResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getConnections() {
            fetch('/api/linkedin/connections')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('connectionResult');
                if (data.success && data.data.elements) {
                    const connections = data.data.elements;
                    let html = '<div class="success"><strong>Your Connections:</strong><br>';
                    connections.slice(0, 5).forEach(conn => {
                        const person = conn['to~'];
                        html += `${person.firstName} ${person.lastName} - ${person.headline}<br>`;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get connections: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendConnectionRequest() {
            const personId = document.getElementById('connectionPersonId').value;
            const message = document.getElementById('connectionMessage').value;
            
            if (!personId) {
                document.getElementById('connectionResult').innerHTML = 
                    '<div class="error">❌ Please enter person ID</div>';
                return;
            }
            
            fetch('/api/linkedin/connection-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    person_id: personId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('connectionResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection request sent!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send connection request: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function searchPeople() {
            const keywords = document.getElementById('searchKeywords').value;
            const company = document.getElementById('searchCompany').value;
            const industry = document.getElementById('searchIndustry').value;
            
            const params = new URLSearchParams();
            if (keywords) params.append('keywords', keywords);
            if (company) params.append('company', company);
            if (industry) params.append('industry', industry);
            
            fetch(`/api/linkedin/search-people?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('searchResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Search completed! Check console for results.</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Search failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('searchResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
