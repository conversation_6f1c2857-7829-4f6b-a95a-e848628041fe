"""
Telegram Messaging Integration via Unipile API and Direct Bot API
Handles authentication and messaging for Telegram using both Unipile and direct Bot API
"""

import requests
import json
import time
import os
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime
import asyncio
import aiohttp

def get_config_path() -> str:
    """Get the absolute path to the config file"""
    # Get the directory where this script is located
    current_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(current_dir, "config.json")

class UnipileClient:
    """Unipile API client for Telegram integration"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api1.unipile.com:13115/api/v1"
        self.headers = {
            "X-API-KEY": self.api_key,
            "accept": "application/json",
            "Content-Type": "application/json"
        }

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to Unipile API"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json() if response.content else {"success": True}

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

class TelegramMessaging:
    def __init__(self, bot_token: str = None, unipile_api_key: str = "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="):
        """Initialize Telegram messaging with both bot token and Unipile API"""
        self.bot_token = bot_token
        self.unipile = UnipileClient(unipile_api_key)

        # Telegram Bot API setup
        self.api_url = "https://api.telegram.org/bot"
        self.base_url = f"{self.api_url}{self.bot_token}" if self.bot_token else ""

        # Bot configuration
        self.bot_info = None
        self.webhook_url = None
        self.connection_status = "disconnected"

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / 30  # 30 messages per second (Telegram limit)

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Load configuration
        self.config = self._load_config()

        # Check for existing bot configuration
        if self.bot_token:
            self._check_bot_status()
        else:
            self.logger.warning("No bot token provided. Bot will not be functional until token is configured.")

    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        config_path = get_config_path()
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                # Update bot token from config if not provided
                if not self.bot_token and config.get("bot_token"):
                    self.bot_token = config["bot_token"]
                    self.base_url = f"{self.api_url}{self.bot_token}"
                return config
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {config_path}")
            return {}

    def _save_config(self):
        """Save configuration to JSON file"""
        config_path = get_config_path()
        try:
            self.config["bot_token"] = self.bot_token
            self.config["webhook_url"] = self.webhook_url
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")

    def _check_bot_status(self):
        """Check bot status and get bot information"""
        try:
            if self.bot_token:
                bot_info = self.get_me()
                if bot_info.get("ok"):
                    self.bot_info = bot_info["result"]
                    self.connection_status = "connected"
                    self.logger.info(f"Bot connected: @{self.bot_info.get('username')}")
                else:
                    self.connection_status = "error"
                    self.logger.error(f"Bot connection failed: {bot_info}")
            else:
                self.connection_status = "not_configured"
        except Exception as e:
            self.logger.error(f"Error checking bot status: {e}")
            self.connection_status = "error"

    def setup_bot(self) -> Dict:
        """Configure Telegram bot"""
        try:
            if not self.bot_token:
                return {"error": "Bot token not provided"}

            # Get bot information
            bot_info = self.get_me()
            if not bot_info.get("ok"):
                return {"error": f"Invalid bot token: {bot_info.get('description', 'Unknown error')}"}

            self.bot_info = bot_info["result"]
            self.connection_status = "connected"

            # Save configuration
            self._save_config()

            return {
                "success": True,
                "bot_info": self.bot_info,
                "status": "configured",
                "message": f"Bot @{self.bot_info.get('username')} configured successfully"
            }

        except Exception as e:
            self.logger.error(f"Bot setup error: {e}")
            return {"error": str(e)}

    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def _make_request(self, method: str, endpoint: str, data: Dict = None, files: Dict = None) -> Dict:
        """Make HTTP request to Telegram Bot API with error handling"""
        if not self.bot_token:
            return {"error": "Bot token not configured"}

        self._rate_limit()

        url = f"{self.base_url}/{endpoint}"

        try:
            if method.upper() == "GET":
                response = requests.get(url, params=data)
            elif method.upper() == "POST":
                if files:
                    response = requests.post(url, data=data, files=files)
                else:
                    response = requests.post(url, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # Parse response even if status code indicates error
            try:
                response_data = response.json()
            except:
                response_data = {"error": f"Invalid JSON response: {response.text}"}

            # Check if request was successful
            if response.status_code == 200 and response_data.get("ok"):
                return response_data
            else:
                # Log detailed error information
                error_msg = f"HTTP {response.status_code}: {response.reason}"
                if response_data.get("description"):
                    error_msg += f" - {response_data['description']}"
                if response_data.get("error_code"):
                    error_msg += f" (Error code: {response_data['error_code']})"

                self.logger.error(f"Telegram API error: {error_msg}")
                self.logger.error(f"Request URL: {url}")
                self.logger.error(f"Request data: {data}")

                # Return the actual Telegram API response for better error handling
                return response_data if response_data.get("ok") is False else {
                    "error": error_msg,
                    "telegram_error": response_data,
                    "status_code": response.status_code,
                    "ok": False
                }

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e), "ok": False}

    def get_me(self) -> Dict:
        """Get basic information about the bot"""
        return self._make_request("GET", "getMe")

    def send_message(self, user_id: Union[int, str], message: str, parse_mode: str = "HTML") -> Dict:
        """Send direct message to user"""
        # Check bot connection status first, and try to connect if not connected
        if self.connection_status != "connected":
            self.logger.info("Bot not connected, attempting to establish connection...")
            connection_test = self.test_connection()
            if not connection_test.get("success"):
                return {
                    "error": "Bot not connected. Please configure bot token first.",
                    "status": self.connection_status,
                    "connection_error": connection_test.get("error")
                }

        try:
            # Try Unipile API first if available
            unipile_result = self._send_via_unipile(user_id, message)
            if unipile_result.get("success"):
                return unipile_result

            # Fallback to direct Telegram Bot API
            data = {
                "chat_id": user_id,
                "text": message,
                "parse_mode": parse_mode
            }

            result = self._make_request("POST", "sendMessage", data)

            if result.get("ok"):
                self.logger.info(f"Message sent successfully to user {user_id}")
                return {
                    "success": True,
                    "message_id": result["result"]["message_id"],
                    "to": user_id,
                    "status": "sent",
                    "ok": True
                }
            else:
                error_msg = result.get("description", result.get("error", "Unknown error"))
                self.logger.error(f"Failed to send message: {error_msg}")
                return {
                    "error": error_msg,
                    "telegram_error": result,
                    "ok": False
                }

        except Exception as e:
            self.logger.error(f"Message sending error: {e}")
            return {"error": str(e), "ok": False}

    def send_group_message(self, group_id: Union[int, str], message: str, parse_mode: str = "HTML") -> Dict:
        """Send message to group"""
        # Check bot connection status first, and try to connect if not connected
        if self.connection_status != "connected":
            self.logger.info("Bot not connected, attempting to establish connection...")
            connection_test = self.test_connection()
            if not connection_test.get("success"):
                return {
                    "error": "Bot not connected. Please configure bot token first.",
                    "status": self.connection_status,
                    "connection_error": connection_test.get("error")
                }

        try:
            data = {
                "chat_id": group_id,
                "text": message,
                "parse_mode": parse_mode
            }

            result = self._make_request("POST", "sendMessage", data)

            if result.get("ok"):
                self.logger.info(f"Message sent successfully to group {group_id}")
                return {
                    "success": True,
                    "message_id": result["result"]["message_id"],
                    "to": group_id,
                    "status": "sent",
                    "ok": True
                }
            else:
                error_msg = result.get("description", result.get("error", "Unknown error"))
                self.logger.error(f"Failed to send group message: {error_msg}")
                return {
                    "error": error_msg,
                    "telegram_error": result,
                    "ok": False
                }

        except Exception as e:
            self.logger.error(f"Group message sending error: {e}")
            return {"error": str(e), "ok": False}

    def _send_via_unipile(self, chat_id: Union[int, str], message: str) -> Dict:
        """Send message via Unipile API (if Telegram account is connected)"""
        try:
            # Check if Telegram account is connected via Unipile
            accounts = self.unipile.make_request("GET", "accounts")

            telegram_account = None
            if "items" in accounts:
                for account in accounts["items"]:
                    if account.get("type") == "TELEGRAM":
                        telegram_account = account
                        break

            if not telegram_account:
                return {"error": "No Telegram account connected via Unipile"}

            # Send message via Unipile
            message_data = {
                "account_id": telegram_account["id"],
                "to": str(chat_id),
                "text": message,
                "type": "text"
            }

            result = self.unipile.make_request("POST", "chats/messages", message_data)

            if "error" not in result:
                self.logger.info(f"Message sent via Unipile to {chat_id}")
                return {
                    "success": True,
                    "message_id": result.get("id"),
                    "to": chat_id,
                    "status": "sent",
                    "via": "unipile"
                }
            else:
                return {"error": result["error"]}

        except Exception as e:
            self.logger.error(f"Unipile message sending error: {e}")
            return {"error": str(e)}

    def send_bulk_messages(self, recipients: List[Union[int, str]], message: str, delay: float = 1.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        if self.connection_status != "connected":
            return [{
                "error": "Bot not connected. Please configure bot token first.",
                "status": self.connection_status
            }]

        results = []

        for chat_id in recipients:
            try:
                result = self.send_message(chat_id, message)
                results.append({
                    "chat_id": chat_id,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })

                # Add delay between messages
                if delay > 0:
                    time.sleep(delay)

            except Exception as e:
                results.append({
                    "chat_id": chat_id,
                    "result": {"error": str(e)},
                    "timestamp": datetime.now().isoformat()
                })

        self.logger.info(f"Bulk messaging completed: {len(results)} messages processed")
        return results

    def send_photo(self, chat_id: Union[int, str], photo: str, caption: str = None, parse_mode: str = "HTML") -> Dict:
        """Send photo to chat"""
        if self.connection_status != "connected":
            return {"error": "Bot not connected"}

        data = {
            "chat_id": chat_id,
            "photo": photo,
            "parse_mode": parse_mode
        }

        if caption:
            data["caption"] = caption

        result = self._make_request("POST", "sendPhoto", data)

        if result.get("ok"):
            self.logger.info(f"Photo sent successfully to chat {chat_id}")
            return {"success": True, "message_id": result["result"]["message_id"]}
        else:
            self.logger.error(f"Failed to send photo: {result}")
            return {"error": result.get("description", "Unknown error")}

    def get_updates(self, offset: int = None, limit: int = 100, timeout: int = 0) -> Dict:
        """Get updates from Telegram"""
        data = {
            "limit": limit,
            "timeout": timeout
        }

        if offset:
            data["offset"] = offset

        return self._make_request("GET", "getUpdates", data)

    def set_webhook(self, webhook_url: str) -> Dict:
        """Set webhook for receiving updates"""
        data = {"url": webhook_url}
        result = self._make_request("POST", "setWebhook", data)

        if result.get("ok"):
            self.webhook_url = webhook_url
            self._save_config()

        return result

    def get_webhook_info(self) -> Dict:
        """Get current webhook status"""
        return self._make_request("GET", "getWebhookInfo")

    def delete_webhook(self) -> Dict:
        """Delete webhook"""
        result = self._make_request("POST", "deleteWebhook")
        if result.get("ok"):
            self.webhook_url = None
            self._save_config()
        return result

    def get_bot_info(self) -> Dict:
        """Get bot information"""
        if self.bot_info:
            return {
                "success": True,
                "bot_info": self.bot_info,
                "status": self.connection_status
            }
        else:
            bot_info = self.get_me()
            if bot_info.get("ok"):
                self.bot_info = bot_info["result"]
                return {
                    "success": True,
                    "bot_info": self.bot_info,
                    "status": "connected"
                }
            else:
                return {"error": bot_info.get("description", "Failed to get bot info")}

    def is_configured(self) -> bool:
        """Check if Telegram bot is properly configured"""
        return bool(self.bot_token) and self.connection_status == "connected"

    def test_connection(self) -> Dict:
        """Test bot connection and return status"""
        try:
            if not self.bot_token:
                return {
                    "success": False,
                    "error": "Bot token not configured",
                    "status": "not_configured"
                }

            # Test connection by getting bot info
            bot_info = self.get_me()

            if bot_info.get("ok"):
                self.bot_info = bot_info["result"]
                self.connection_status = "connected"
                return {
                    "success": True,
                    "message": f"Bot @{self.bot_info.get('username')} is connected and working",
                    "bot_info": self.bot_info,
                    "status": "connected"
                }
            else:
                self.connection_status = "error"
                return {
                    "success": False,
                    "error": bot_info.get("description", "Connection test failed"),
                    "status": "error"
                }

        except Exception as e:
            self.logger.error(f"Connection test error: {e}")
            self.connection_status = "error"
            return {
                "success": False,
                "error": str(e),
                "status": "error"
            }

    def update_config(self, bot_token: str = None, webhook_url: str = None) -> Dict:
        """Update bot configuration"""
        try:
            if bot_token:
                self.bot_token = bot_token
                self.base_url = f"{self.api_url}{self.bot_token}"
                # Re-check bot status
                self._check_bot_status()

            if webhook_url:
                self.webhook_url = webhook_url

            self._save_config()

            return {
                "success": True,
                "message": "Configuration updated successfully",
                "status": self.connection_status
            }

        except Exception as e:
            self.logger.error(f"Config update error: {e}")
            return {"error": str(e)}

# Legacy TelegramAPI class for backward compatibility
class TelegramAPI:
    def __init__(self, config_path: str = None):
        """Initialize Telegram API client"""
        # Use the same path resolution as the new TelegramMessaging class
        self.config_path = config_path if config_path else get_config_path()
        self.config = self._load_config()
        self.bot_token = self.config.get("bot_token")
        self.api_url = self.config.get("api_url", "https://api.telegram.org/bot")
        self.base_url = f"{self.api_url}{self.bot_token}" if self.bot_token else ""

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 30)

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, files: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.bot_token:
            return {"error": "Bot token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=data)
            elif method.upper() == "POST":
                if files:
                    response = requests.post(url, data=data, files=files)
                else:
                    response = requests.post(url, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def get_me(self) -> Dict:
        """Get basic information about the bot"""
        return self._make_request("GET", "getMe")
    
    def send_message(self, chat_id: Union[int, str], text: str, 
                    parse_mode: str = None, reply_markup: Dict = None) -> Dict:
        """Send text message to chat"""
        if not self.bot_token:
            return {"error": "Telegram bot not configured properly"}
        
        data = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode or self.config.get("settings", {}).get("parse_mode", "HTML")
        }
        
        if reply_markup:
            data["reply_markup"] = json.dumps(reply_markup)
        
        result = self._make_request("POST", "sendMessage", data)
        
        if result.get("ok"):
            self.logger.info(f"Message sent successfully to chat {chat_id}")
        else:
            self.logger.error(f"Failed to send message: {result}")
        
        return result
    
    def send_photo(self, chat_id: Union[int, str], photo: str, 
                  caption: str = None, parse_mode: str = None) -> Dict:
        """Send photo to chat"""
        data = {
            "chat_id": chat_id,
            "photo": photo,
            "parse_mode": parse_mode or self.config.get("settings", {}).get("parse_mode", "HTML")
        }
        
        if caption:
            data["caption"] = caption
        
        result = self._make_request("POST", "sendPhoto", data)
        
        if result.get("ok"):
            self.logger.info(f"Photo sent successfully to chat {chat_id}")
        else:
            self.logger.error(f"Failed to send photo: {result}")
        
        return result
    
    def send_document(self, chat_id: Union[int, str], document: str, 
                     caption: str = None, parse_mode: str = None) -> Dict:
        """Send document to chat"""
        data = {
            "chat_id": chat_id,
            "document": document,
            "parse_mode": parse_mode or self.config.get("settings", {}).get("parse_mode", "HTML")
        }
        
        if caption:
            data["caption"] = caption
        
        result = self._make_request("POST", "sendDocument", data)
        
        if result.get("ok"):
            self.logger.info(f"Document sent successfully to chat {chat_id}")
        else:
            self.logger.error(f"Failed to send document: {result}")
        
        return result
    
    def get_updates(self, offset: int = None, limit: int = 100, 
                   timeout: int = 0) -> Dict:
        """Get updates from Telegram"""
        data = {
            "limit": limit,
            "timeout": timeout
        }
        
        if offset:
            data["offset"] = offset
        
        return self._make_request("GET", "getUpdates", data)
    
    def set_webhook(self, webhook_url: str, certificate: str = None) -> Dict:
        """Set webhook for receiving updates"""
        data = {
            "url": webhook_url
        }
        
        files = {}
        if certificate:
            files["certificate"] = open(certificate, 'rb')
        
        result = self._make_request("POST", "setWebhook", data, files)
        
        if files:
            files["certificate"].close()
        
        return result
    
    def delete_webhook(self) -> Dict:
        """Delete webhook"""
        return self._make_request("POST", "deleteWebhook")
    
    def get_webhook_info(self) -> Dict:
        """Get current webhook status"""
        return self._make_request("GET", "getWebhookInfo")
    
    def send_bulk_messages(self, recipients: List[Union[int, str]], 
                          message: str, delay: float = 1.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        results = []
        
        for chat_id in recipients:
            result = self.send_message(chat_id, message)
            results.append({
                "chat_id": chat_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, chat_id: Union[int, str], 
                            template_name: str, **kwargs) -> Dict:
        """Send predefined template message"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}
        
        message = templates[template_name].format(**kwargs)
        return self.send_message(chat_id, message)
    
    def is_configured(self) -> bool:
        """Check if Telegram bot is properly configured"""
        return bool(self.bot_token)
    
    def update_config(self, bot_token: str = None, webhook_url: str = None):
        """Update configuration"""
        if bot_token:
            self.config["bot_token"] = bot_token
            self.bot_token = bot_token
            self.base_url = f"{self.api_url}{self.bot_token}"
        
        if webhook_url:
            self.config["webhook_url"] = webhook_url
        
        self._save_config()
        self.logger.info("Telegram configuration updated")

# Example usage
if __name__ == "__main__":
    print("🤖 Telegram Integration Example")
    print("=" * 50)

    # Example 1: Using new TelegramMessaging class
    print("\n📱 Using TelegramMessaging class:")
    telegram = TelegramMessaging(bot_token="YOUR_BOT_TOKEN")

    # Setup bot
    setup_result = telegram.setup_bot()
    print(f"Setup result: {setup_result}")

    if telegram.is_configured():
        # Get bot info
        bot_info = telegram.get_bot_info()
        print(f"Bot info: {bot_info}")

        # Send message to user
        # result = telegram.send_message("@username", "Hello from Telegram!")
        # print(f"Message result: {result}")

        # Send message to group
        # group_result = telegram.send_group_message("-1001234567890", "Hello group!")
        # print(f"Group message result: {group_result}")

        # Send bulk messages
        # recipients = ["@user1", "@user2", "123456789"]
        # bulk_results = telegram.send_bulk_messages(recipients, "Bulk message!")
        # print(f"Bulk results: {bulk_results}")
    else:
        print("❌ Telegram bot not configured properly")

    print("\n💡 To use this integration:")
    print("1. Get a bot token from @BotFather on Telegram")
    print("2. Initialize: telegram = TelegramMessaging(bot_token='YOUR_TOKEN')")
    print("3. Setup bot: telegram.setup_bot()")
    print("4. Send messages: telegram.send_message(user_id, 'Hello!')")
    print("5. Send to groups: telegram.send_group_message(group_id, 'Hello group!')")

    # Example 2: Using legacy TelegramAPI class
    print("\n🔄 Using legacy TelegramAPI class:")
    legacy_telegram = TelegramAPI()

    if not legacy_telegram.is_configured():
        print("❌ Legacy Telegram bot not configured. Please update config.json with your bot token.")
    else:
        # Example: Get bot info
        bot_info = legacy_telegram.get_me()
        print(f"Legacy bot info: {bot_info}")

        # Example: Send a test message (replace with actual chat ID)
        # result = legacy_telegram.send_message("@username", "Hello from legacy Telegram Bot!")
        # print(f"Legacy message result: {result}")
